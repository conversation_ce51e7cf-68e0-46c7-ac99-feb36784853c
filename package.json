{"name": "church-lms", "version": "1.0.0", "description": "Church Membership Learning Management System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@supabase/auth-helpers-nextjs": "^0.8.0", "@supabase/auth-helpers-react": "^0.4.0", "@supabase/supabase-js": "^2.38.0", "dotenv": "^16.5.0", "emailjs-com": "^3.2.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.292.0", "next": "^14.0.0", "pdf-lib": "^1.17.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-pdf": "^7.5.1", "react-signature-canvas": "^1.1.0-alpha.2", "signature_pad": "^4.1.7"}, "devDependencies": {"autoprefixer": "^10.4.16", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "worker-loader": "^3.0.8"}}