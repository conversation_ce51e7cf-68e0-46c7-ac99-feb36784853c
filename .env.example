# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# EmailJS Configuration (for sending emails)
# Get these from https://www.emailjs.com/
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_emailjs_service_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_emailjs_template_id
NEXT_PUBLIC_EMAILJS_USER_ID=your_emailjs_user_id

# Admin Configuration
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>

# Instructions for EmailJS Setup:
# 1. Go to https://www.emailjs.com/ and create an account
# 2. Create a new service (Gmail, Outlook, etc.)
# 3. Create an email template with the following variables:
#    - {{to_email}} - recipient email
#    - {{to_name}} - recipient name
#    - {{subject}} - email subject
#    - {{message}} - email message body
#    - {{pdf_url}} - PDF download link (for agreement emails)
#    - {{temp_password}} - temporary password (for invitation emails)
# 4. Copy your Service ID, Template ID, and User ID to the variables above
