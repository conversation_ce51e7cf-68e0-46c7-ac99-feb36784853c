import { supabase } from '../../../lib/supabase-admin'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { userId } = req.query

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    // Get user's tag to determine which journey they should see
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('user_tag')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user:', userError)
      return res.status(500).json({ error: 'Failed to fetch user data' })
    }

    const userTag = userData.user_tag || 'newcomer'

    // Get all active journeys for this user tag
    const { data: journeys, error: journeyError } = await supabase
      .from('journeys')
      .select(`
        *,
        journey_days (
          id,
          day_number,
          title,
          content,
          scripture_reference,
          reflection_questions
        )
      `)
      .eq('user_tag', userTag)
      .eq('is_active', true)
      .order('created_at', { ascending: true }) // Get oldest first (5-day before 14-day)

    if (journeyError) {
      console.error('Error fetching journeys:', journeyError)
      return res.status(500).json({ error: 'Failed to fetch journey data' })
    }

    if (!journeys || journeys.length === 0) {
      return res.status(404).json({ error: 'No journeys found for this user tag' })
    }

    // Determine which journey the user should be on
    let currentJourney = null
    let allProgress = []

    // Get progress for all journeys
    for (const journey of journeys) {
      const { data: journeyProgress, error: progressError } = await supabase
        .from('user_journey_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('journey_id', journey.id)

      if (progressError) {
        console.error('Error fetching progress:', progressError)
        continue
      }

      allProgress.push(...(journeyProgress || []))

      // Check if this journey is completed
      const completedDays = journeyProgress?.filter(p => p.completed).length || 0
      const totalDays = journey.journey_days?.length || 0
      const isCompleted = completedDays === totalDays && totalDays > 0

      // If no current journey set, or this journey is not completed, use this one
      if (!currentJourney || !isCompleted) {
        currentJourney = journey
        if (!isCompleted) break // Stop at first incomplete journey
      }
    }

    if (!currentJourney) {
      return res.status(404).json({ error: 'No available journey found' })
    }

    // Get user's progress for the current journey
    const progress = allProgress.filter(p => p.journey_id === currentJourney.id)

    // Sort journey days by day_number
    if (currentJourney.journey_days) {
      currentJourney.journey_days.sort((a, b) => a.day_number - b.day_number)
    }

    // Add progress information to each day
    const daysWithProgress = currentJourney.journey_days.map(day => {
      const dayProgress = progress.find(p => p.day_id === day.id)
      return {
        ...day,
        completed: dayProgress?.completed || false,
        reflection_answers: dayProgress?.reflection_answers || null,
        completed_at: dayProgress?.completed_at || null
      }
    })

    // Check if there are other journeys available
    const completedJourneys = []
    const availableJourneys = []

    for (const journey of journeys) {
      const journeyProgress = allProgress.filter(p => p.journey_id === journey.id)
      const completedDays = journeyProgress.filter(p => p.completed).length
      const totalDays = journey.journey_days?.length || 0
      const isCompleted = completedDays === totalDays && totalDays > 0

      if (isCompleted) {
        completedJourneys.push(journey)
      } else if (journey.id !== currentJourney.id) {
        availableJourneys.push(journey)
      }
    }

    return res.status(200).json({
      success: true,
      journey: {
        ...currentJourney,
        journey_days: daysWithProgress
      },
      progress: {
        completedDays: progress.filter(p => p.completed).length,
        totalDays: currentJourney.journey_days.length,
        progressPercentage: Math.round((progress.filter(p => p.completed).length / currentJourney.journey_days.length) * 100)
      },
      completedJourneys,
      availableJourneys
    })

  } catch (error) {
    console.error('Error in user-journey API:', error)
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message 
    })
  }
}
