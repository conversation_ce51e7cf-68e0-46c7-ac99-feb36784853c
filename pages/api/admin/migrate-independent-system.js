import { supabase } from '../../../lib/supabase-admin'
import fs from 'fs'
import path from 'path'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    console.log('Starting independent tags and journeys migration...')

    // Read the migration SQL file
    const sqlFilePath = path.join(process.cwd(), 'sql', 'independent_tags_journeys_migration.sql')
    const migrationSQL = fs.readFileSync(sqlFilePath, 'utf8')

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    console.log(`Executing ${statements.length} SQL statements...`)

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement.trim()) {
        try {
          console.log(`Executing statement ${i + 1}/${statements.length}`)
          const { error } = await supabase.rpc('exec_sql', { sql_query: statement })
          
          if (error) {
            console.error(`Error in statement ${i + 1}:`, error)
            // Continue with other statements unless it's a critical error
            if (error.message.includes('already exists') || error.message.includes('does not exist')) {
              console.log('Skipping non-critical error:', error.message)
            } else {
              throw error
            }
          }
        } catch (statementError) {
          console.error(`Failed to execute statement ${i + 1}:`, statementError)
          // Try direct execution for some statements
          try {
            const { error: directError } = await supabase.from('_temp').select('1').limit(0)
            if (directError) {
              console.log('Direct execution also failed, continuing...')
            }
          } catch (e) {
            // Ignore
          }
        }
      }
    }

    // Verify the migration by checking if new tables exist
    const { data: userTagsCheck } = await supabase
      .from('user_tags')
      .select('id')
      .limit(1)

    const { data: userJourneysCheck } = await supabase
      .from('user_journeys')
      .select('id')
      .limit(1)

    if (userTagsCheck !== null && userJourneysCheck !== null) {
      console.log('Migration completed successfully!')
      return res.status(200).json({ 
        success: true, 
        message: 'Independent tags and journeys system migration completed successfully'
      })
    } else {
      throw new Error('Migration verification failed - new tables not accessible')
    }

  } catch (error) {
    console.error('Migration error:', error)
    return res.status(500).json({ 
      success: false, 
      error: 'Migration failed: ' + error.message 
    })
  }
}
