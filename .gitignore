# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea/
.vscode/

# OS
Thumbs.db

# Logs
logs
*.log

# Cache
.cache/
.parcel-cache/

# Build outputs
dist/
build/

# Temporary files
**/temp/
**/tmp/
*.tmp
*.temp

# Diagnostic files
**/diagnostics/
**/*-diagnostics.js
**/*-test.js
**/email-logs.js

# Fix files
**/*-fix.js
**/*-fix.sql
**/*-fix.md

# Backup files
*.bak
*.backup
*~
