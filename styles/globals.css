@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
  /* Improve mobile text rendering */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Loading animation */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Improve touch targets */
  button,
  input[type="button"],
  input[type="submit"],
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better mobile form inputs */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  input[type="date"],
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px;
    border-radius: 8px;
  }

  /* Mobile-friendly spacing */
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Improve mobile text readability */
  h1 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  h3 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Better mobile card layouts */
  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Improve mobile navigation */
  .mobile-nav-item {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }

  /* Better mobile buttons */
  .mobile-button {
    width: 100%;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  /* Improve mobile tables */
  table {
    font-size: 0.875rem;
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  /* Better mobile modals */
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }

  /* Mobile-specific PDF viewer improvements */
  .pdf-viewer-container {
    height: 60vh;
    min-height: 400px;
  }

  /* Mobile signature pad improvements */
  .signature-pad-container {
    min-height: 200px;
  }

  /* Mobile form improvements */
  .form-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  /* Mobile progress tracker improvements */
  .progress-tracker {
    position: static;
    width: 100%;
    margin-bottom: 1rem;
  }

  /* Mobile multimedia content improvements */
  .multimedia-content-item {
    margin-bottom: 1.5rem;
  }

  .multimedia-content-item iframe {
    min-height: 200px;
  }

  /* Mobile dashboard improvements */
  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Mobile section navigation improvements */
  .section-navigation {
    flex-direction: column;
    gap: 0.5rem;
  }

  .section-navigation button {
    width: 100%;
    justify-content: center;
  }
}

/* Tablet-specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Optimize for tablet layouts */
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .tablet-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Input field improvements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="date"],
textarea,
select {
  background-color: white !important;
  color: #1f2937 !important;
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="tel"]::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
}

/* Ensure text is visible in all input states */
input:focus,
textarea:focus,
select:focus {
  background-color: white !important;
  color: #1f2937 !important;
}

/* Multimedia content styles */
.multimedia-content-item {
  scroll-margin-top: 2rem;
}

.multimedia-content-item .prose {
  max-width: none;
}

.multimedia-content-item .prose h1,
.multimedia-content-item .prose h2,
.multimedia-content-item .prose h3,
.multimedia-content-item .prose h4,
.multimedia-content-item .prose h5,
.multimedia-content-item .prose h6 {
  color: #1f2937;
  font-weight: 600;
}

.multimedia-content-item .prose p {
  color: #374151;
  line-height: 1.7;
}

.multimedia-content-item .prose a {
  color: #2563eb;
  text-decoration: underline;
}

.multimedia-content-item .prose a:hover {
  color: #1d4ed8;
}

.multimedia-content-item .prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  font-style: italic;
  color: #6b7280;
}

.multimedia-content-item .prose ul,
.multimedia-content-item .prose ol {
  padding-left: 1.5rem;
}

.multimedia-content-item .prose li {
  margin: 0.5rem 0;
}

/* Video responsive container */
.multimedia-content-item .aspect-video {
  aspect-ratio: 16 / 9;
}

/* Audio player styling */
.multimedia-content-item audio {
  width: 100%;
  height: 40px;
}

/* Embed container responsive */
.multimedia-content-item .embed-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
}

.multimedia-content-item .embed-container iframe,
.multimedia-content-item .embed-container object,
.multimedia-content-item .embed-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
